#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#
import os
import sys
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(BASE_DIR, '..')))
import torch
from random import randint
from utils.loss_utils import l1_loss, ssim
from gaussian_renderer import network_gui
from gaussian_renderer_cullSH3 import render_depth, render_imp
import sys
from scene import Scene, GaussianModelcullSH3
from utils.general_utils import safe_state
import uuid
from tqdm import tqdm
from utils.image_utils import psnr
from argparse import ArgumentParser, Namespace
from arguments import ModelParams, PipelineParams, OptimizationParams
from lpipsPyTorch import lpips
from optimizing_spa import OptimizingSpa
from optimizing_spa_sh3 import OptimizingSpaSH3


try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_FOUND = True
except ImportError:
    TENSORBOARD_FOUND = False
import numpy as np
from lpipsPyTorch import lpips
from utils.sh_utils import SH2RGB

def check_render_inputs_nan(gaussians, iteration):
    """检查渲染器输入是否包含NaN，只在发现NaN时打印"""
    has_nan = False
    
    # 检查xyz坐标
    xyz = gaussians.get_xyz
    if torch.isnan(xyz).any():
        print(f"迭代{iteration}: 渲染器输入xyz包含NaN")
        has_nan = True
    
    # 检查opacity
    opacity = gaussians.get_opacity
    if torch.isnan(opacity).any():
        print(f"迭代{iteration}: 渲染器输入opacity包含NaN")
        has_nan = True
    
    # 检查scaling
    scaling = gaussians.get_scaling
    if torch.isnan(scaling).any():
        print(f"迭代{iteration}: 渲染器输入scaling包含NaN")
        has_nan = True
    
    # 检查rotation
    rotation = gaussians.get_rotation
    if torch.isnan(rotation).any():
        print(f"迭代{iteration}: 渲染器输入rotation包含NaN")
        has_nan = True
    
    # 检查features
    features = gaussians.get_features
    if torch.isnan(features).any():
        print(f"迭代{iteration}: 渲染器输入features包含NaN")
        has_nan = True
    
    # 检查features_rest_opacity
    features_rest_opacity = gaussians.get_features_rest_opacity
    if torch.isnan(features_rest_opacity).any():
        print(f"迭代{iteration}: 渲染器输入features_rest_opacity包含NaN")
        has_nan = True
    
    return has_nan

def check_render_outputs_nan(render_pkg, iteration):
    """检查渲染器输出是否包含NaN，只在发现NaN时打印"""
    has_nan = False
    
    # 检查渲染图像
    rendered_image = render_pkg["render"]
    if torch.isnan(rendered_image).any():
        print(f"迭代{iteration}: 渲染器输出rendered_image包含NaN")
        has_nan = True
    
    # 检查viewspace_points（如果存在）
    if "viewspace_points" in render_pkg:
        viewspace_points = render_pkg["viewspace_points"]
        if torch.isnan(viewspace_points).any():
            print(f"迭代{iteration}: 渲染器输出viewspace_points包含NaN")
            has_nan = True
    
    # 检查radii（如果存在）
    if "radii" in render_pkg:
        radii = render_pkg["radii"]
        if torch.isnan(radii).any():
            print(f"迭代{iteration}: 渲染器输出radii包含NaN")
            has_nan = True
    
    # 检查其他可能的输出
    for key, value in render_pkg.items():
        if isinstance(value, torch.Tensor) and key not in ["render", "viewspace_points", "radii"]:
            if torch.isnan(value).any():
                print(f"迭代{iteration}: 渲染器输出{key}包含NaN")
                has_nan = True
    
    return has_nan

def training(dataset, opt, pipe, testing_iterations, saving_iterations, checkpoint_iterations, checkpoint, debug_from, args):
    first_iter = 0
    tb_writer = prepare_output_and_logger(dataset)
    gaussians = GaussianModelcullSH3(dataset.sh_degree)
    scene = Scene(dataset, gaussians)
    imp_score = torch.zeros(gaussians._xyz.shape[0], device='cuda')
    gaussians.training_setup(opt)
    if checkpoint:
        (model_params, first_iter) = torch.load(checkpoint)
        gaussians.restore(model_params, opt)
    bg_color = [1, 1, 1] if dataset.white_background else [0, 0, 0]
    background = torch.tensor(bg_color, dtype=torch.float32, device="cuda")

    iter_start = torch.cuda.Event(enable_timing = True)
    iter_end = torch.cuda.Event(enable_timing = True)

    viewpoint_stack = None
    ema_loss_for_log = 0.0
    progress_bar = tqdm(range(first_iter, opt.iterations), desc="Training progress")
    first_iter += 1
    device = "cuda"
    mask_blur = torch.zeros(gaussians._xyz.shape[0], device='cuda')

    for iteration in range(first_iter, opt.iterations + 1):        
        if network_gui.conn == None:
            network_gui.try_connect()
        while network_gui.conn != None:
            try:
                net_image_bytes = None
                custom_cam, do_training, pipe.convert_SHs_python, pipe.compute_cov3D_python, keep_alive, scaling_modifer = network_gui.receive()
                if custom_cam != None:
                    net_image = render_imp(custom_cam, gaussians, pipe, background, scaling_modifer)["render"]
                    net_image_bytes = memoryview((torch.clamp(net_image, min=0, max=1.0) * 255).byte().permute(1, 2, 0).contiguous().cpu().numpy())
                network_gui.send(net_image_bytes, dataset.source_path)
                if do_training and ((iteration < int(opt.iterations)) or not keep_alive):
                    break
            except Exception as e:
                network_gui.conn = None

        iter_start.record()

        simp_iteration1=args.simp_iteration1 
        if iteration<simp_iteration1:
            gaussians.update_learning_rate(iteration,opt)
        else:
            gaussians.update_learning_rate(iteration-simp_iteration1+5000,opt)
        # Every 1000 its we increase the levels of SH up to a maximum degree
        if iteration % 1000 == 0 and iteration>simp_iteration1:
            gaussians.oneupSHdegree()

        # Pick a random Camera
        if not viewpoint_stack:
            viewpoint_stack = scene.getTrainCameras().copy()

        viewpoint_cam = viewpoint_stack.pop(randint(0, len(viewpoint_stack)-1))

        if (iteration - 1) == debug_from:
            pipe.debug = True
        bg = torch.rand((3), device="cuda") if opt.random_background else background
        
        check_render_inputs_nan(gaussians, iteration)
        render_pkg = render_imp(viewpoint_cam, gaussians, pipe, background, lambda_sh_sparsity=0.1)
        check_render_outputs_nan(render_pkg, iteration)
        image, viewspace_point_tensor, visibility_filter, radii = render_pkg["render"], render_pkg["viewspace_points"], render_pkg["visibility_filter"], render_pkg["radii"]
        
        # Loss
        gt_image = viewpoint_cam.original_image.cuda()
        Ll1 = l1_loss(image, gt_image)
        ssim_value = ssim(image, gt_image)
        loss = (1.0 - opt.lambda_dssim) * Ll1 + opt.lambda_dssim * (1.0 - ssim_value)
        Llag = loss
        if opt.optimizing_spa == True and iteration > args.optimizing_spa_start_iter and iteration % opt.optimizing_spa_interval == 0 and iteration < args.optimizing_spa_stop_iter:
            temp = loss 
            loss = optimizingSpa.append_spa_loss(loss)
            loss = optimizingSpaSH3.append_feature_rest_loss(loss)
            Llag = temp - loss
        torch.cuda.synchronize()
        loss.backward()
        torch.cuda.synchronize()
        iter_end.record()

        with torch.no_grad():
            # Progress bar
            ema_loss_for_log = 0.4 * loss.item() + 0.6 * ema_loss_for_log
            if iteration % 10 == 0:
                progress_bar.set_postfix({"Loss": f"{ema_loss_for_log:.{7}f}"})
                progress_bar.update(10)
            if iteration == opt.iterations:
                progress_bar.close()

            # Log and save
            training_report(tb_writer, opt,iteration, Ll1, loss, l1_loss, Llag, iter_start.elapsed_time(iter_end), testing_iterations, scene, render_imp, (pipe, background), args)
            if (iteration in saving_iterations):
                scene.save(iteration)

            # Densification
            if iteration < opt.densify_until_iter:
                # Keep track of max radii in image-space for pruning
                gaussians.max_radii2D[visibility_filter] = torch.max(gaussians.max_radii2D[visibility_filter], radii[visibility_filter])
                gaussians.add_densification_stats(viewspace_point_tensor, visibility_filter)
                area_max = render_pkg["area_max"]
                mask_blur = torch.logical_or(mask_blur, area_max>(image.shape[1]*image.shape[2]/5000))
                if iteration > opt.densify_from_iter and iteration % opt.densification_interval == 0 and iteration % 5000!=0 and gaussians._xyz.shape[0]<args.num_max:  
                    size_threshold = 20 if iteration > opt.opacity_reset_interval else None
                    gaussians.densify_and_prune_split(opt.densify_grad_threshold, 
                                                    0.005, scene.cameras_extent, 
                                                    size_threshold, mask_blur)
                    mask_blur = torch.zeros(gaussians._xyz.shape[0], device='cuda')
                if iteration%5000==0:
                    torch.cuda.synchronize()
                    out_pts_list=[]
                    gt_list=[]
                    views=scene.getTrainCameras()
                    for view in views:
                        gt = view.original_image[0:3, :, :]
                        render_depth_pkg = render_depth(view, gaussians, pipe, background)
                        out_pts = render_depth_pkg["out_pts"]
                        accum_alpha = render_depth_pkg["accum_alpha"]
                        prob=1-accum_alpha
                        prob = prob/prob.sum()
                        prob = prob.reshape(-1).cpu().numpy()
                        factor=1/(image.shape[1]*image.shape[2]*len(views)/args.num_depth)
                        N_xyz=prob.shape[0]
                        num_sampled=int(N_xyz*factor)
                        indices = np.random.choice(N_xyz, size=num_sampled, 
                                                   p=prob,replace=False)
                        out_pts = out_pts.permute(1,2,0).reshape(-1,3)
                        gt = gt.permute(1,2,0).reshape(-1,3)
                        out_pts_list.append(out_pts[indices])
                        gt_list.append(gt[indices])
                               
                    out_pts_merged=torch.cat(out_pts_list)
                    gt_merged=torch.cat(gt_list)
                    gaussians.reinitial_pts(out_pts_merged, gt_merged)
                    gaussians.training_setup(opt)
                    mask_blur = torch.zeros(gaussians._xyz.shape[0], device='cuda')
                    torch.cuda.empty_cache()
                    viewpoint_stack = scene.getTrainCameras().copy()
            elif iteration == args.optimizing_spa_start_iter and opt.optimizing_spa == True:
                imp_score = update_imp_score( scene, args, gaussians, pipe, background)
                optimizingSpa = OptimizingSpa(gaussians, opt, device,imp_score_flag=True)
                torch.cuda.synchronize()
                imp_score_features_rest, _ = update_feature_rest_score( scene, args, gaussians, pipe, background)
                torch.cuda.synchronize()
                optimizingSpaSH3 = OptimizingSpaSH3(gaussians, opt, device,imp_score_flag=True)
                optimizingSpa.update(imp_score, update_u=False)
                optimizingSpaSH3.update(imp_score_features_rest, update_u=False)
            elif iteration % opt.optimizing_spa_interval == 0 and opt.optimizing_spa == True and (iteration > args.optimizing_spa_start_iter and iteration <= args.optimizing_spa_stop_iter):
                imp_score = update_imp_score( scene, args, gaussians, pipe, background)
                torch.cuda.synchronize()
                imp_score_features_rest, _ = update_feature_rest_score( scene, args, gaussians, pipe, background)
                torch.cuda.synchronize()
                optimizingSpa.update(imp_score)
                optimizingSpaSH3.update(imp_score_features_rest)
            if iteration == simp_iteration1:
                imp_score = update_imp_score( scene, args, gaussians, pipe, background)
                torch.cuda.synchronize()
                imp_score_features_rest, _ = update_feature_rest_score( scene, args, gaussians, pipe, background)

                prob = (imp_score+1)/(imp_score+1).sum()
                prob = prob.cpu().numpy()


                N_xyz = gaussians._xyz.shape[0]
                num_sampled=int(N_xyz*(1-opt.prune_ratio1))
            
                indices = np.random.choice(N_xyz, size=num_sampled, 
                                             p=prob, replace=False)
    
                mask = np.zeros(N_xyz, dtype=bool)
                mask[indices] = True


                # print(mask.sum(), mask.sum()/mask.shape[0])  
                gaussians.prune_points(mask==False) 
                gaussians.max_sh_degree=dataset.sh_degree
                gaussians.reinitial_pts(gaussians._xyz, 
                                    SH2RGB(gaussians._features_dc+0)[:,0])
                gaussians.training_setup(opt)
                torch.cuda.empty_cache()
                viewpoint_stack = scene.getTrainCameras().copy()
                

            if iteration == args.optimizing_spa_stop_iter:
                imp_score = update_imp_score( scene, args, gaussians, pipe, background)
                mask = np.zeros(imp_score.shape[0], dtype=bool)
                threshold = int(opt.prune_ratio2 * imp_score.shape[0])
                print("prune_ratio2:",opt.prune_ratio2)
                print("imp_score.shape[0]:",imp_score.shape[0])
                print("threshold for pruning:",threshold)
                imp_score_sort = torch.zeros(imp_score.shape)
                imp_score_sort, _ = torch.sort(imp_score,0)
                imp_score_threshold = imp_score_sort[threshold-1]
                mask = (imp_score <= imp_score_threshold).squeeze()
                print("\nBefore sparsifyting pruning:",gaussians.get_opacity.shape[0]) 
                gaussians.prune_points(mask)
                print("\nAfter sparsifying pruning:",gaussians.get_opacity.shape[0])
                # SH剪枝有问题
                # 1.报错：剪完之后，features_rest_opacity和features_rest又包含NaN。有可能是剪完直接赋值为0然后渲染器寄掉。要看看reduced-3dgs为什么能跑起来    
                # 2.仔细看看下述代码是否有误，可以看一看reduced-3dgs原函数内部的操作
                # 总结一下就是如何处理剪枝完之后的SH 
                # 因为有一部分的Gaussian的feature_rest是0，所以后面迭代的过程中会出现等于0的情况，导致后面的计算出现nan。所以要把想办法针对那些个feature_rest=0的点进行处理。如何进行迭代（锁参数？或者最后一轮迭代才剪掉feature？）
                torch.cuda.synchronize()
                imp_score_features_rest, weighted_mean = update_feature_rest_score( scene, args, gaussians, pipe, background)
                torch.cuda.synchronize()
                features_rest_threshold = imp_score_features_rest.shape[0] * args.features_rest_threshold
                print("features_rest_threshold:",args.features_rest_threshold)
                print("imp_score_features_rest.shape[0]:",imp_score_features_rest.shape[0])
                print("threshold for feature_rest pruning:",features_rest_threshold)
                std_mask = imp_score_features_rest < features_rest_threshold
                gaussians._features_dc[std_mask] = (weighted_mean[std_mask] - 0.5) / 0.28209479177387814
                gaussians._degrees[std_mask] = 0
                gaussians._features_rest[std_mask] = 0
                gaussians._features_rest_opacity[std_mask] = torch.full_like(gaussians._features_rest_opacity[std_mask], -1e7)
                torch.cuda.empty_cache()     


            # Optimizer step
            if iteration < opt.iterations:
                gaussians.optimizer.step()
                gaussians.optimizer.zero_grad(set_to_none = True)

            if (iteration in checkpoint_iterations):
                print("\n[ITER {}] Saving Checkpoint".format(iteration))
                torch.save((gaussians.capture(), iteration), scene.model_path + "/chkpnt" + str(iteration) + ".pth")
                
            # if iteration in args.cull_SH:
            #     print("\n[ITER {}] Pruning SH Bands".format(iteration))
            #     gaussians.cull_sh_bands(scene.getTrainCameras(), threshold=args.cdist_threshold*np.sqrt(3)/255, std_threshold=args.std_threshold)
                # torch.cuda.synchronize()
                # imp_score_features_rest, weighted_mean = update_feature_rest_score( scene, args, gaussians, pipe, background)
                # std_mask = imp_score_features_rest < threshold
                # gaussians._features_dc[std_mask] = (weighted_mean[std_mask] - 0.5) / 0.28209479177387814
                # gaussians._degrees[std_mask] = 0
                # # 因为这一行_features_rest剪枝为0，导致后续计算时得到nan
                # gaussians._features_rest[std_mask] = 0
                # torch.cuda.empty_cache()  
    print("Final gaussians number after pruning: ", gaussians.get_xyz.shape[0])        
    return

def prepare_output_and_logger(args):    
    if not args.model_path:
        if os.getenv('OAR_JOB_ID'):
            unique_str=os.getenv('OAR_JOB_ID')
        else:
            unique_str = str(uuid.uuid4())
        args.model_path = os.path.join("./output/", unique_str[0:10])
        
    # Set up output folder
    print("Output folder: {}".format(args.model_path))
    os.makedirs(args.model_path, exist_ok = True)
    with open(os.path.join(args.model_path, "cfg_args"), 'w') as cfg_log_f:
        cfg_log_f.write(str(Namespace(**vars(args))))

    # Create Tensorboard writer
    tb_writer = None
    if TENSORBOARD_FOUND:
        tb_writer = SummaryWriter(args.model_path)
    else:
        print("Tensorboard not available: not logging progress")
    return tb_writer

def training_report(tb_writer, opt, iteration, Ll1, loss, l1_loss, Llag, elapsed, testing_iterations, scene : Scene, renderFunc, renderArgs, args):
    if tb_writer:
        tb_writer.add_scalar('train_loss_patches/l1_loss', Ll1.item(), iteration)
        tb_writer.add_scalar('train_loss_patches/total_loss', loss.item(), iteration)
        if opt.optimizing_spa == True and iteration > args.optimizing_spa_start_iter and iteration <= args.optimizing_spa_stop_iter:
            tb_writer.add_scalar('train_loss_patches/lagrange_loss', Llag.item(), iteration)
        tb_writer.add_scalar('iter_time', elapsed, iteration)
        tb_writer.add_scalar('total_points', scene.gaussians.get_xyz.shape[0], iteration)
    # Report test and samples of training set
    if iteration in testing_iterations:
        torch.cuda.empty_cache()
        validation_configs = ({'name': 'test', 'cameras' : scene.getTestCameras()}, 
                              {'name': 'train', 'cameras' : [scene.getTrainCameras()[idx % len(scene.getTrainCameras())] for idx in range(5, 30, 5)]})

        for config in validation_configs:
            if config['cameras'] and len(config['cameras']) > 0:
                l1_test = 0.0
                psnr_test = 0.0
                ssims = []
                lpipss = []
                for idx, viewpoint in enumerate(config['cameras']):
                    image = torch.clamp(renderFunc(viewpoint, scene.gaussians, *renderArgs)["render"], 0.0, 1.0)
                    gt_image = torch.clamp(viewpoint.original_image.to("cuda"), 0.0, 1.0)
                    if tb_writer and (idx < 5):
                        if iteration == testing_iterations[-1]:
                            tb_writer.add_images(config['name'] + "_view_{}/render".format(viewpoint.image_name), image[None], global_step=iteration)
                        if iteration == testing_iterations[0]:
                            tb_writer.add_images(config['name'] + "_view_{}/ground_truth".format(viewpoint.image_name), gt_image[None], global_step=iteration)
                    l1_test += l1_loss(image, gt_image).mean().double()
                    psnr_test += psnr(image, gt_image).mean().double()

                    ssims.append(ssim(image, gt_image))
                    lpipss.append(lpips(image, gt_image, net_type='vgg'))

                psnr_test /= len(config['cameras'])
                l1_test /= len(config['cameras']) 
                ssims_test=torch.tensor(ssims).mean()
                lpipss_test=torch.tensor(lpipss).mean()  
                print("\n[ITER {}] Evaluating {}: L1 {} PSNR {} SSIM {} LPIPS {}".format(iteration, config['name'], l1_test, psnr_test, ssims_test, lpipss_test))
                #print("\n[ITER {}] Evaluating {}: L1 {} PSNR {}".format(iteration, config['name'], l1_test, psnr_test))
                if tb_writer:
                    tb_writer.add_scalar(config['name'] + '/loss_viewpoint - l1_loss', l1_test, iteration)
                    tb_writer.add_scalar(config['name'] + '/loss_viewpoint - psnr', psnr_test, iteration)
                    tb_writer.add_scalar(config['name'] + '/loss_viewpoint - ssim', ssims_test, iteration)
                    tb_writer.add_scalar(config['name'] + '/loss_viewpoint - lpips', lpipss_test, iteration)

        if tb_writer and iteration == testing_iterations:
            tb_writer.add_histogram("scene/opacity_histogram", scene.gaussians.get_opacity, iteration)
            #tb_writer.add_scalar('total_points', scene.gaussians.get_xyz.shape[0], iteration)
        torch.cuda.empty_cache()

def update_imp_score( scene, args, gaussians, pipe, background):
    imp_score = torch.zeros(gaussians._xyz.shape[0]).cuda()
    accum_area_max = torch.zeros(gaussians._xyz.shape[0]).cuda()
    views = scene.getTrainCameras()
    for view in views:
        # print(idx)
        render_pkg = render_imp(view, gaussians, pipe, background)
        accum_weights = render_pkg["accum_weights"]
        area_proj = render_pkg["area_proj"]
        area_max = render_pkg["area_max"]

        accum_area_max = accum_area_max+area_max

        if args.imp_metric=='outdoor':
            mask_t=area_max!=0
            temp = imp_score+accum_weights/area_proj
            imp_score[mask_t] = temp[mask_t]
        else:
            imp_score = imp_score + accum_weights
    
    imp_score[accum_area_max==0]=0
    return imp_score

def update_feature_rest_score( scene, args, gaussians, pipe, background):
    imp_score = torch.zeros(gaussians._xyz.shape[0]).cuda()
    views = scene.getTrainCameras()
    torch.cuda.synchronize()
    imp_score, weighted_mean = gaussians.calculate_features_rest_imp(views)
    torch.cuda.synchronize()
    return imp_score, weighted_mean

if __name__ == "__main__":
    # Set up command line argument parser
    parser = ArgumentParser(description="Training script parameters")
    lp = ModelParams(parser)
    op = OptimizationParams(parser)
    pp = PipelineParams(parser)
    parser.add_argument('--ip', type=str, default="127.0.0.1")
    parser.add_argument('--port', type=int, default=6009)
    parser.add_argument('--debug_from', type=int, default=-1)
    parser.add_argument('--detect_anomaly', action='store_true', default=False)
    #parser.add_argument("--test_iterations", nargs="+", type=int, default=range(10000,op.iterations+1000,1000))
    parser.add_argument("--test_iterations", nargs="+", type=int, default=[op.iterations])
    parser.add_argument("--save_iterations", nargs="+", type=int, default=[op.iterations])
    parser.add_argument("--quiet", action="store_true")
    parser.add_argument("--checkpoint_iterations", nargs="+", type=int, default=[50000])
    parser.add_argument("--start_checkpoint", type=str, default = None)
    parser.add_argument("--simp_iteration1", type=int, default = 8_000)
    parser.add_argument("--num_depth", type=int, default = 3_500_000)
    parser.add_argument("--num_max", type=int, default = 4_500_000)
    # parser.add_argument("--cull_SH", nargs="+", type=int, default=[20000])
    parser.add_argument("--imp_metric", required=True, type=str, default = None)
    parser.add_argument("--optimizing_spa_start_iter", type=int, default = 15_200)
    parser.add_argument("--optimizing_spa_stop_iter", type=int, default = 25_200)
    # parser.add_argument("--std_threshold", type=float, default = 0.04)
    # parser.add_argument("--cdist_threshold", type=float, default = 6)
    parser.add_argument("--features_rest_threshold", type=float, default = 0.75)
    
    args = parser.parse_args(sys.argv[1:])
    args.save_iterations.append(args.iterations)

    print("Optimizing " + args.model_path)

    # Initialize system state (RNG)
    safe_state(args.quiet)

    # Start GUI server, configure and run training
    network_gui.init(args.ip, args.port)
    torch.autograd.set_detect_anomaly(args.detect_anomaly)
    training(lp.extract(args), op.extract(args), pp.extract(args), args.test_iterations, args.save_iterations, args.checkpoint_iterations, args.start_checkpoint, args.debug_from, args)

    # All done
    print("\nTraining complete.")
